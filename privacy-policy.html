<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />

    <!-- font awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
      integrity="sha512-..."
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <title>Serv5</title>
  </head>

  <body
    class="flex flex-col gap-4 md:gap-8 lg:gap-12 2xl:gap-16 overflow-x-hidden"
  >
    <header class="bg-main-blue relative">
      <!-- start of the  logo -->
      <div class="container flex justify-between h-[127px] items-center">
        <svg
          width="101"
          height="98"
          class="size-[98px] header-logo"
          viewBox="0 0 101 98"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <!-- Outer circle (first to animate) -->
          <path
            id="logo-outer-circle"
            d="M84.9157 80.1711C76.8332 87.5147 65.6703 92.0596 53.3487 92.0596C28.7056 92.0596 8.69727 73.8802 8.69727 51.4897C8.69727 29.0993 28.7056 10.9199 53.3487 10.9199"
            stroke="#DE8545"
            stroke-width="4.6875"
            stroke-linecap="round"
            fill="none"
          />
          <!-- Inner circle (second to animate) -->
          <path
            id="logo-inner-circle"
            d="M53.3486 10.9199C77.9917 10.9199 98 29.0993 98 51.4897C98 73.8802 77.9917 92.0596 53.3486 92.0596C48.5334 92.0596 43.8953 91.3655 39.5488 90.0816"
            stroke="#DE8545"
            stroke-width="4.6875"
            stroke-linecap="round"
            fill="none"
          />
          <!-- Base rectangle (third to animate) -->
          <path
            id="logo-base-rectangle"
            d="M52.2359 98.0003H11.1552C5.01991 98.0003 0 93.4395 0 87.865C0 82.2905 5.01991 77.7295 11.1552 77.7295H52.2359C58.3713 77.7295 63.3911 82.2905 63.3911 87.865C63.3911 93.4395 58.3713 98.0003 52.2359 98.0003Z"
            fill="#15205C"
            stroke="#DE8545"
            stroke-width="0"
          />
          <!-- Text/Icons (fourth to animate) -->
          <g id="logo-text-icons">
            <path
              class="logo-letter"
              d="M14.4084 85.3265C14.1143 84.3605 13.5939 83.2712 12.1689 83.2712C10.9021 83.2712 10.3027 84.0831 10.3027 84.9051C10.3027 85.9635 11.0831 86.4774 12.4629 87.0837C13.8994 87.7208 15.3923 88.4299 15.3923 90.0845C15.3923 91.6978 13.8767 92.9824 11.5694 92.9824C10.8681 92.9824 10.3027 92.8694 9.87296 92.7358C9.44313 92.6124 9.20561 92.4891 9.03603 92.4171C8.92287 92.1191 8.67414 90.7524 8.57227 90.0227L9.08116 89.8891C9.3639 90.8449 10.1896 92.4171 11.9202 92.4171C13.1529 92.4171 13.8768 91.7286 13.8768 90.6495C13.8768 89.5603 12.972 89.0465 11.6826 88.4299C10.5063 87.8956 8.88894 87.1454 8.88894 85.4908C8.88894 83.9905 10.2235 82.7471 12.429 82.7471C13.2321 82.7471 13.9898 82.9218 14.6232 83.1068C14.691 83.6411 14.7702 84.2577 14.9172 85.2442L14.4084 85.3265Z"
              fill="#DE8545"
            />
            <path
              class="logo-letter"
              d="M25.1869 90.3099C25.0738 90.8751 24.7457 92.2625 24.5987 92.7454H16.2969V92.3138C17.8238 92.2111 17.9708 92.0672 17.9708 90.834V84.9456C17.9708 83.5685 17.8238 83.5275 16.5118 83.4247V82.9727H24.1802C24.2028 83.363 24.2706 84.3599 24.3386 85.2025L23.8184 85.2643C23.66 84.6373 23.4789 84.288 23.2641 83.9591C23.0152 83.6303 22.5515 83.5275 21.3866 83.5275H20.131C19.5768 83.5275 19.543 83.5685 19.543 84.0207V87.3913H21.0925C22.608 87.3913 22.7211 87.2682 22.936 86.2507H23.445V89.2001H22.936C22.6986 88.1004 22.5741 88.0183 21.0925 88.0183H19.543V90.7826C19.543 91.4917 19.6335 91.8308 19.9501 92.0158C20.2781 92.1905 20.9115 92.1905 21.6806 92.1905C22.9134 92.1905 23.4223 92.0877 23.8069 91.6972C24.101 91.3787 24.4176 90.8545 24.6665 90.2482L25.1869 90.3099Z"
              fill="#DE8545"
            />
            <path
              class="logo-letter"
              d="M36.2622 92.879C36.0586 92.879 35.8664 92.8688 35.6628 92.8482C34.2942 92.7865 33.593 92.4577 32.8126 91.4712C32.2358 90.7415 31.6816 89.8269 31.1726 89.1076C30.8672 88.6759 30.5732 88.491 29.7022 88.491H29.2273V90.834C29.2273 92.0979 29.3856 92.2111 30.7428 92.3138V92.7454H26.1621V92.3138C27.5307 92.2111 27.689 92.0979 27.689 90.834V84.8737C27.689 83.5891 27.542 83.5069 26.23 83.4247V82.9727H30.5618C31.8399 82.9727 32.6656 83.1062 33.3102 83.4966C34.0001 83.8872 34.43 84.5552 34.43 85.4697C34.43 86.7748 33.5026 87.5558 32.2923 88.0183C32.5864 88.5115 33.2763 89.4981 33.7853 90.1866C34.396 90.9779 34.7579 91.3992 35.1538 91.7692C35.5836 92.2111 35.9568 92.3755 36.3414 92.4577L36.2622 92.879ZM29.985 87.9874C30.8333 87.9874 31.3987 87.864 31.8286 87.5558C32.4732 87.1037 32.7335 86.5179 32.7335 85.7061C32.7335 84.0926 31.5685 83.4966 30.3129 83.4966C29.804 83.4966 29.5326 83.5584 29.4082 83.6405C29.295 83.7228 29.2273 83.8974 29.2273 84.288V87.9874H29.985Z"
              fill="#DE8545"
            />
            <path
              class="logo-letter"
              d="M47.2054 83.4247C46.0518 83.5377 45.8141 83.7021 45.1468 85.0792C44.683 86.0555 42.9187 89.7961 41.6066 92.9098H41.0298C39.8875 90.1558 38.4511 86.9188 37.5011 84.7812C36.992 83.6612 36.7093 83.5069 35.6914 83.435V82.9727H40.0007V83.4247C38.7678 83.5377 38.7566 83.6713 39.0167 84.3496C39.4804 85.4286 40.7472 88.3573 41.8216 90.7723H41.8441C42.7716 88.7377 43.9593 85.9937 44.4795 84.6065C44.8188 83.7227 44.7283 83.5685 43.3484 83.4247V82.9727H47.2054V83.4247Z"
              fill="#DE8545"
            />
            <path
              class="logo-letter"
              d="M49.5919 87.3093C50.2818 87.0729 51.1075 86.8365 51.5485 86.8365C53.6974 86.8365 54.8173 88.1211 54.8173 89.3645C54.8173 90.2791 54.3309 91.1115 53.2791 91.8822C52.3629 92.5193 51.2206 92.9202 50.3157 92.9202C49.558 92.9202 48.8453 92.6221 48.574 92.3962C48.3251 92.1803 48.2346 92.057 48.2686 91.8616C48.2686 91.6973 48.4382 91.4609 48.6532 91.3068C48.8115 91.204 48.9472 91.204 49.1168 91.3274C49.49 91.6253 50.2026 92.1494 51.2545 92.1494C52.623 92.1494 53.2791 91.0807 53.2791 89.9914C53.2791 88.6555 52.3629 87.7306 50.5646 87.7306C49.8407 87.7306 49.1961 87.9155 48.8002 88.0286L49.4788 83.4248H54.3987L54.5005 83.5378L54.0368 84.6168H50.033L49.5919 87.3093Z"
              fill="#DE8545"
            />
            <path
              class="logo-icon"
              d="M55.4247 0H56.7964C56.8458 0.0246814 56.8921 0.0596832 56.945 0.0727212C59.5635 0.716344 60.7644 2.86669 59.7677 5.14651C59.2144 6.41208 58.6382 7.66938 58.0845 8.93474C57.8685 9.42846 57.6858 9.93419 57.4878 10.4344C57.407 10.5157 57.2931 10.5848 57.2509 10.6801C56.9786 11.2952 56.7216 11.9157 56.4603 12.5348C56.0419 13.5264 55.6241 14.5184 55.2061 15.5101C55.1242 15.6648 55.0325 15.8159 54.9619 15.9746C53.9279 18.2971 52.8966 20.6206 51.8647 22.9438C50.8327 25.2672 49.8065 27.5928 48.767 29.9134C48.0078 31.6082 46.1724 32.5722 44.3537 32.2499C41.7707 31.7922 40.3793 29.507 41.3334 27.2672C41.8675 26.0133 42.4286 24.7688 42.9671 23.544C42.8816 23.1901 42.8147 22.8671 42.7252 22.5494C42.2066 20.7099 41.4807 18.9575 40.1214 17.4737C39.5633 16.9424 39.0463 16.368 38.4381 15.8892C37.2331 14.9402 35.8555 14.2739 34.2565 14.0419C31.5012 13.5398 28.9524 13.9091 26.7367 15.5686C25.3568 16.6021 24.4801 17.8941 24.4541 19.5752C24.4129 19.7185 24.3246 19.8657 24.3389 20.0042C24.3947 20.5443 24.4363 21.0913 24.5686 21.618C24.8788 22.8525 25.4523 23.9791 26.391 24.934C26.5235 25.1351 26.629 25.3567 26.7927 25.5338C27.7984 26.6223 28.9601 27.5432 30.286 28.3095C31.9011 29.2429 33.4741 30.2363 35.0684 31.1997C36.1384 31.8463 37.2158 32.4827 38.2861 33.129C41.3919 35.004 44.5448 36.833 47.2642 39.168C48.9088 40.58 50.4845 42.0582 51.7042 43.8387C52.6133 45.1656 53.3738 46.5389 53.9229 48.0006C54.8725 50.5281 55.15 53.1352 54.8573 55.7992C54.4484 59.5217 53.0273 62.8719 50.2761 65.6639C44.3354 71.6926 36.7972 73.6354 28.1724 72.5071C26.3798 72.2726 24.6425 71.6955 22.8748 71.2943C22.1074 71.1202 21.3274 70.9917 20.6339 70.858C20.0577 71.2993 19.7734 71.7973 19.6408 72.4982C18.4475 74.6642 17.0417 75.3768 14.7209 74.9923C14.5521 74.919 14.3852 74.8417 14.2142 74.773C12.8143 74.2108 12.0251 73.218 11.8234 71.8615C11.7541 71.395 11.8539 70.9078 11.8764 70.4299C12.8938 68.1477 13.9111 65.8655 14.9285 63.5832C15.8547 61.5287 16.7885 59.477 17.7052 57.419C19.0576 54.3825 20.3931 51.3396 21.7453 48.303C22.3909 46.8529 23.5788 45.9991 25.3014 45.929C28.1445 45.8131 30.2687 48.261 29.1576 50.8921C27.7091 54.3216 26.1434 57.7101 24.6285 61.1165C24.3521 61.7382 24.0772 62.3605 23.7801 63.0314C24.2092 63.3795 24.5757 63.7598 25.0242 64.0258C26.6954 65.017 28.4817 65.7478 30.506 65.9144C34.0947 66.2098 37.2482 65.4815 39.5056 62.7212C41.5034 60.2783 41.8772 57.6559 40.4039 54.8734C39.5932 53.3425 38.4358 52.0404 37.1359 50.8381C34.6009 48.4935 31.6556 46.6094 28.7373 44.6965C25.5967 42.6377 22.4437 40.5943 19.316 38.5195C18.4452 37.9419 17.6447 37.2771 16.812 36.6522C16.2078 36.0812 15.5446 35.5518 15.0138 34.9293C14.2637 34.0495 13.6115 33.1009 12.9174 32.1817C12.9045 32.1137 12.9069 32.0394 12.8765 31.9786C11.8476 29.9147 11.4662 27.7315 11.4016 25.4855C11.3013 21.9913 12.0287 18.6743 14.0017 15.6403C16.244 12.1918 19.4768 9.77352 23.4851 8.1489C26.5217 6.91818 29.7003 6.28437 33.0012 6.13835C37.615 5.93425 42.0943 6.61149 46.4615 7.94561C47.4594 8.25054 48.4478 8.58132 49.4626 8.90727C50.3527 6.90236 51.28 4.95216 52.0829 2.9605C52.6895 1.45553 53.6803 0.399987 55.4247 0Z"
              fill="#2458A7"
            />
            <path
              class="logo-icon"
              d="M61.4293 17.3108H66.2065C68.3308 17.3197 70.4552 17.3357 72.5795 17.3357C74.7416 17.3356 76.9038 17.3196 79.0659 17.3105C80.1093 17.3105 81.1528 17.3105 82.2397 17.3105C82.4814 16.6209 82.7241 15.9357 82.9617 15.2491C83.6356 13.3025 85.8093 12.2428 87.9217 12.8328C90.0475 13.4266 91.2142 15.4589 90.548 17.4087C89.7385 19.778 88.9285 22.1472 88.1187 24.5164C87.5134 26.2267 86.908 27.937 86.3031 29.6473C86.2169 29.8911 86.1333 30.1356 86.0485 30.3797C86.0028 30.4334 85.9527 30.4845 85.9121 30.5412C84.7456 32.1723 82.6243 32.7534 80.7813 31.9456C78.9182 31.1292 78.0841 29.2351 78.762 27.384C79.0378 26.6307 79.2925 25.8709 79.5526 25.1128C79.6461 24.8404 79.7269 24.5645 79.8383 24.2116H66.8508C65.473 27.6652 64.0931 31.1242 62.6844 34.6553C62.9697 34.6116 63.1348 34.5891 63.2987 34.5608C67.7437 33.7909 72.1577 33.8927 76.5196 35.0112C82.5933 36.5687 87.6532 39.4903 91.2743 44.269C92.121 45.3864 92.8161 46.5986 93.581 47.7673C95.0354 50.8007 96.0776 53.9422 95.9567 57.2763C95.6538 65.6337 90.9819 72.7107 83.0579 77.1835C82.8326 77.3107 83.2826 77.0557 83.0579 77.1835C83.0075 77.2147 83.1082 77.1523 83.0579 77.1835C83.0179 77.2229 83.0979 77.1443 83.0579 77.1835C82.7679 77.285 83.3335 77.0565 83.0579 77.1835C79.4099 78.8651 74.6148 80.1694 70.5251 80.0128C62.0075 79.6865 55.0993 76.5363 50.0989 70.157C50.0774 70.1297 50.0675 70.0948 50.0293 70.0163C51.519 68.5236 53.0239 67.0157 54.5414 65.4952C55.1319 65.5267 55.7173 65.5579 56.1404 65.5805C57.2816 66.7397 58.2374 67.901 59.3971 68.8584C69.2744 77.0136 83.4219 72.2073 87.0103 62.1986C90.2255 53.2312 83.6993 43.3419 73.3653 41.5232C69.705 40.879 66.0924 41.1678 62.5053 41.92C60.9152 42.2534 59.353 42.6967 57.7781 43.0898C57.5969 43.1351 57.4151 43.1785 57.2096 43.2286C55.6099 41.9478 54.0152 40.6708 52.4036 39.3805C53.1658 37.4712 53.9153 35.5908 54.667 33.7109C56.4433 29.2687 58.2394 24.833 59.9756 20.378"
              fill="#DE8545"
            />
          </g>

          <!-- Gradient definition for shine effect -->
          <defs>
            <linearGradient
              id="shineGradient"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="0%"
            >
              <stop offset="0%" style="stop-color: white; stop-opacity: 0" />
              <stop offset="50%" style="stop-color: white; stop-opacity: 0.2" />
              <stop offset="100%" style="stop-color: white; stop-opacity: 0" />
            </linearGradient>
          </defs>

          <!-- Shine effect overlay (hidden initially) -->
          <rect
            id="logo-shine"
            x="-20"
            y="0"
            width="20"
            height="98"
            fill="url(#shineGradient)"
            opacity="0"
          />
        </svg>

        <!-- end of the  logo -->

        <!-- links -->
        <ul class="hidden lg:flex items-center gap-10 text-white font-light">
          <!-- main -->
          <li
            class="nav-item active text-primary-yellow flex gap-1.5 items-center relative font-semibold transition-all duration-300"
          >
            <a href="/" class="flex gap-2 items-center">
              <i class="fa-solid fa-house"></i>
              <span data-nav="home">الرئيسية</span>
            </a>
            <!-- floating arrow -->
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[60px] h-[20px] absolute -bottom-5 left-1/2 animate-scale-x opacity-100"
            />
          </li>
          <!--نبذة عنا  -->
          <li
            class="nav-item relative transition-all duration-300 hover:text-primary-yellow"
          >
            <a class="flex gap-2 items-center" href="/">
              <i class="fa-solid fa-5"></i>
              <span data-nav="about">نبذة عنا</span>
            </a>
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[50px] h-[20px] absolute -bottom-3 left-1/2 animate-scale-x opacity-0 transition-opacity duration-300"
            />
          </li>
          <!-- خدماتنا   -->
          <li
            class="nav-item relative transition-all duration-300 hover:text-primary-yellow"
          >
            <a class="flex gap-2 items-center" href="/">
              <i class="fa-solid fa-plus"></i>
              <span data-nav="services">خدماتنا</span>
            </a>
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[50px] h-[20px] absolute -bottom-3 left-1/2 animate-scale-x opacity-0 transition-opacity duration-300"
            />
          </li>
          <!-- أعمالــنا   -->
          <li
            class="nav-item relative transition-all duration-300 hover:text-primary-yellow"
          >
            <a class="flex gap-2 items-center" href="/">
              <i class="fa-solid fa-briefcase"></i>
              <span data-nav="portfolio">أعمالــنا</span>
            </a>
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[50px] h-[20px] absolute -bottom-3 left-1/2 animate-scale-x opacity-0 transition-opacity duration-300"
            />
          </li>
          <!-- المدونــة   -->
          <li
            class="nav-item relative transition-all duration-300 hover:text-primary-yellow"
          >
            <a class="flex gap-2 items-center" href="/">
              <i class="fa-regular fa-rectangle-list"></i>
              <span data-nav="blog">المدونــة</span>
            </a>
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[50px] h-[20px] absolute -bottom-3 left-1/2 animate-scale-x opacity-0 transition-opacity duration-300"
            />
          </li>
          <!-- الكتيب التعريفي   -->
          <li
            class="nav-item relative transition-all duration-300 hover:text-primary-yellow"
          >
            <a class="flex gap-2 items-center" href="/">
              <i class="fa-solid fa-file-pdf"></i>
              <span data-nav="brochure">الكتيب التعريفي</span>
            </a>
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[50px] h-[20px] absolute -bottom-3 left-1/2 animate-scale-x opacity-0 transition-opacity duration-300"
            />
          </li>
        </ul>
        <!-- Desktop Navigation -->
        <div class="hidden lg:flex gap-2 items-center">
          <!-- button to switch lang  -->
          <button
            id="langToggle"
            class="rounded-md text-primary-blue font-bold bg-white p-4 hover:bg-gray-100 transition-all duration-300 header-button"
          >
            EN
          </button>
          <!-- company profile button  -->
          <button
            class="rounded-md flex items-center gap-2 text-white border border-white font-bold bg-transparent p-4 hover:bg-white hover:text-primary-blue transition-all duration-300 header-button header-profile-btn"
          >
            <i class="fa-solid fa-book"></i>
            <span data-nav="profile">بروفايل الشركة</span>
          </button>

          <!-- our vision -->
          <img
            src="/public/shared/header/our-vision.webp"
            alt="Serv5 future vision for 2030"
            class="w-[125px] h-[84px] ms-8 header-vision"
          />
        </div>

        <!-- Mobile Navigation -->
        <div class="lg:hidden flex items-center gap-4">
          <!-- Mobile Language Toggle -->
          <button
            id="mobileLangToggle"
            class="rounded-md text-primary-blue font-bold bg-white p-3 hover:bg-gray-100 transition-all duration-300 text-sm"
          >
            EN
          </button>

          <!-- Burger Menu Button -->
          <button
            id="burgerMenuBtn"
            class="relative w-8 h-8 flex flex-col justify-center items-center space-y-1 focus:outline-none burger-menu-btn"
            aria-label="Toggle mobile menu"
          >
            <span
              class="burger-line w-6 h-0.5 bg-white transition-all duration-300 ease-in-out"
            ></span>
            <span
              class="burger-line w-6 h-0.5 bg-white transition-all duration-300 ease-in-out"
            ></span>
            <span
              class="burger-line w-6 h-0.5 bg-white transition-all duration-300 ease-in-out"
            ></span>
          </button>
        </div>
      </div>

      <!-- Mobile Menu Overlay -->
      <div
        id="mobileMenuOverlay"
        class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden opacity-0 pointer-events-none transition-opacity duration-300"
      ></div>

      <!-- Mobile Menu -->
      <div
        id="mobileMenu"
        class="fixed top-0 w-80 h-full bg-main-blue z-50 lg:hidden transform transition-transform duration-300 ease-in-out mobile-menu"
      >
        <!-- Mobile Menu Header -->
        <div
          class="flex items-center justify-between p-6 border-b border-gray-700"
        >
          <div class="flex items-center gap-3">
            <svg
              width="101"
              height="98"
              class="size-[98px] header-logo"
              viewBox="0 0 101 98"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <!-- Outer circle (first to animate) -->
              <path
                id="logo-outer-circle"
                d="M84.9157 80.1711C76.8332 87.5147 65.6703 92.0596 53.3487 92.0596C28.7056 92.0596 8.69727 73.8802 8.69727 51.4897C8.69727 29.0993 28.7056 10.9199 53.3487 10.9199"
                stroke="#DE8545"
                stroke-width="4.6875"
                stroke-linecap="round"
                fill="none"
              />
              <!-- Inner circle (second to animate) -->
              <path
                id="logo-inner-circle"
                d="M53.3486 10.9199C77.9917 10.9199 98 29.0993 98 51.4897C98 73.8802 77.9917 92.0596 53.3486 92.0596C48.5334 92.0596 43.8953 91.3655 39.5488 90.0816"
                stroke="#DE8545"
                stroke-width="4.6875"
                stroke-linecap="round"
                fill="none"
              />
              <!-- Base rectangle (third to animate) -->
              <path
                id="logo-base-rectangle"
                d="M52.2359 98.0003H11.1552C5.01991 98.0003 0 93.4395 0 87.865C0 82.2905 5.01991 77.7295 11.1552 77.7295H52.2359C58.3713 77.7295 63.3911 82.2905 63.3911 87.865C63.3911 93.4395 58.3713 98.0003 52.2359 98.0003Z"
                fill="#15205C"
                stroke="#DE8545"
                stroke-width="0"
              />
              <!-- Text/Icons (fourth to animate) -->
              <g id="logo-text-icons">
                <path
                  class="logo-letter"
                  d="M14.4084 85.3265C14.1143 84.3605 13.5939 83.2712 12.1689 83.2712C10.9021 83.2712 10.3027 84.0831 10.3027 84.9051C10.3027 85.9635 11.0831 86.4774 12.4629 87.0837C13.8994 87.7208 15.3923 88.4299 15.3923 90.0845C15.3923 91.6978 13.8767 92.9824 11.5694 92.9824C10.8681 92.9824 10.3027 92.8694 9.87296 92.7358C9.44313 92.6124 9.20561 92.4891 9.03603 92.4171C8.92287 92.1191 8.67414 90.7524 8.57227 90.0227L9.08116 89.8891C9.3639 90.8449 10.1896 92.4171 11.9202 92.4171C13.1529 92.4171 13.8768 91.7286 13.8768 90.6495C13.8768 89.5603 12.972 89.0465 11.6826 88.4299C10.5063 87.8956 8.88894 87.1454 8.88894 85.4908C8.88894 83.9905 10.2235 82.7471 12.429 82.7471C13.2321 82.7471 13.9898 82.9218 14.6232 83.1068C14.691 83.6411 14.7702 84.2577 14.9172 85.2442L14.4084 85.3265Z"
                  fill="#DE8545"
                />
                <path
                  class="logo-letter"
                  d="M25.1869 90.3099C25.0738 90.8751 24.7457 92.2625 24.5987 92.7454H16.2969V92.3138C17.8238 92.2111 17.9708 92.0672 17.9708 90.834V84.9456C17.9708 83.5685 17.8238 83.5275 16.5118 83.4247V82.9727H24.1802C24.2028 83.363 24.2706 84.3599 24.3386 85.2025L23.8184 85.2643C23.66 84.6373 23.4789 84.288 23.2641 83.9591C23.0152 83.6303 22.5515 83.5275 21.3866 83.5275H20.131C19.5768 83.5275 19.543 83.5685 19.543 84.0207V87.3913H21.0925C22.608 87.3913 22.7211 87.2682 22.936 86.2507H23.445V89.2001H22.936C22.6986 88.1004 22.5741 88.0183 21.0925 88.0183H19.543V90.7826C19.543 91.4917 19.6335 91.8308 19.9501 92.0158C20.2781 92.1905 20.9115 92.1905 21.6806 92.1905C22.9134 92.1905 23.4223 92.0877 23.8069 91.6972C24.101 91.3787 24.4176 90.8545 24.6665 90.2482L25.1869 90.3099Z"
                  fill="#DE8545"
                />
                <path
                  class="logo-letter"
                  d="M36.2622 92.879C36.0586 92.879 35.8664 92.8688 35.6628 92.8482C34.2942 92.7865 33.593 92.4577 32.8126 91.4712C32.2358 90.7415 31.6816 89.8269 31.1726 89.1076C30.8672 88.6759 30.5732 88.491 29.7022 88.491H29.2273V90.834C29.2273 92.0979 29.3856 92.2111 30.7428 92.3138V92.7454H26.1621V92.3138C27.5307 92.2111 27.689 92.0979 27.689 90.834V84.8737C27.689 83.5891 27.542 83.5069 26.23 83.4247V82.9727H30.5618C31.8399 82.9727 32.6656 83.1062 33.3102 83.4966C34.0001 83.8872 34.43 84.5552 34.43 85.4697C34.43 86.7748 33.5026 87.5558 32.2923 88.0183C32.5864 88.5115 33.2763 89.4981 33.7853 90.1866C34.396 90.9779 34.7579 91.3992 35.1538 91.7692C35.5836 92.2111 35.9568 92.3755 36.3414 92.4577L36.2622 92.879ZM29.985 87.9874C30.8333 87.9874 31.3987 87.864 31.8286 87.5558C32.4732 87.1037 32.7335 86.5179 32.7335 85.7061C32.7335 84.0926 31.5685 83.4966 30.3129 83.4966C29.804 83.4966 29.5326 83.5584 29.4082 83.6405C29.295 83.7228 29.2273 83.8974 29.2273 84.288V87.9874H29.985Z"
                  fill="#DE8545"
                />
                <path
                  class="logo-letter"
                  d="M47.2054 83.4247C46.0518 83.5377 45.8141 83.7021 45.1468 85.0792C44.683 86.0555 42.9187 89.7961 41.6066 92.9098H41.0298C39.8875 90.1558 38.4511 86.9188 37.5011 84.7812C36.992 83.6612 36.7093 83.5069 35.6914 83.435V82.9727H40.0007V83.4247C38.7678 83.5377 38.7566 83.6713 39.0167 84.3496C39.4804 85.4286 40.7472 88.3573 41.8216 90.7723H41.8441C42.7716 88.7377 43.9593 85.9937 44.4795 84.6065C44.8188 83.7227 44.7283 83.5685 43.3484 83.4247V82.9727H47.2054V83.4247Z"
                  fill="#DE8545"
                />
                <path
                  class="logo-letter"
                  d="M49.5919 87.3093C50.2818 87.0729 51.1075 86.8365 51.5485 86.8365C53.6974 86.8365 54.8173 88.1211 54.8173 89.3645C54.8173 90.2791 54.3309 91.1115 53.2791 91.8822C52.3629 92.5193 51.2206 92.9202 50.3157 92.9202C49.558 92.9202 48.8453 92.6221 48.574 92.3962C48.3251 92.1803 48.2346 92.057 48.2686 91.8616C48.2686 91.6973 48.4382 91.4609 48.6532 91.3068C48.8115 91.204 48.9472 91.204 49.1168 91.3274C49.49 91.6253 50.2026 92.1494 51.2545 92.1494C52.623 92.1494 53.2791 91.0807 53.2791 89.9914C53.2791 88.6555 52.3629 87.7306 50.5646 87.7306C49.8407 87.7306 49.1961 87.9155 48.8002 88.0286L49.4788 83.4248H54.3987L54.5005 83.5378L54.0368 84.6168H50.033L49.5919 87.3093Z"
                  fill="#DE8545"
                />
                <path
                  class="logo-icon"
                  d="M55.4247 0H56.7964C56.8458 0.0246814 56.8921 0.0596832 56.945 0.0727212C59.5635 0.716344 60.7644 2.86669 59.7677 5.14651C59.2144 6.41208 58.6382 7.66938 58.0845 8.93474C57.8685 9.42846 57.6858 9.93419 57.4878 10.4344C57.407 10.5157 57.2931 10.5848 57.2509 10.6801C56.9786 11.2952 56.7216 11.9157 56.4603 12.5348C56.0419 13.5264 55.6241 14.5184 55.2061 15.5101C55.1242 15.6648 55.0325 15.8159 54.9619 15.9746C53.9279 18.2971 52.8966 20.6206 51.8647 22.9438C50.8327 25.2672 49.8065 27.5928 48.767 29.9134C48.0078 31.6082 46.1724 32.5722 44.3537 32.2499C41.7707 31.7922 40.3793 29.507 41.3334 27.2672C41.8675 26.0133 42.4286 24.7688 42.9671 23.544C42.8816 23.1901 42.8147 22.8671 42.7252 22.5494C42.2066 20.7099 41.4807 18.9575 40.1214 17.4737C39.5633 16.9424 39.0463 16.368 38.4381 15.8892C37.2331 14.9402 35.8555 14.2739 34.2565 14.0419C31.5012 13.5398 28.9524 13.9091 26.7367 15.5686C25.3568 16.6021 24.4801 17.8941 24.4541 19.5752C24.4129 19.7185 24.3246 19.8657 24.3389 20.0042C24.3947 20.5443 24.4363 21.0913 24.5686 21.618C24.8788 22.8525 25.4523 23.9791 26.391 24.934C26.5235 25.1351 26.629 25.3567 26.7927 25.5338C27.7984 26.6223 28.9601 27.5432 30.286 28.3095C31.9011 29.2429 33.4741 30.2363 35.0684 31.1997C36.1384 31.8463 37.2158 32.4827 38.2861 33.129C41.3919 35.004 44.5448 36.833 47.2642 39.168C48.9088 40.58 50.4845 42.0582 51.7042 43.8387C52.6133 45.1656 53.3738 46.5389 53.9229 48.0006C54.8725 50.5281 55.15 53.1352 54.8573 55.7992C54.4484 59.5217 53.0273 62.8719 50.2761 65.6639C44.3354 71.6926 36.7972 73.6354 28.1724 72.5071C26.3798 72.2726 24.6425 71.6955 22.8748 71.2943C22.1074 71.1202 21.3274 70.9917 20.6339 70.858C20.0577 71.2993 19.7734 71.7973 19.6408 72.4982C18.4475 74.6642 17.0417 75.3768 14.7209 74.9923C14.5521 74.919 14.3852 74.8417 14.2142 74.773C12.8143 74.2108 12.0251 73.218 11.8234 71.8615C11.7541 71.395 11.8539 70.9078 11.8764 70.4299C12.8938 68.1477 13.9111 65.8655 14.9285 63.5832C15.8547 61.5287 16.7885 59.477 17.7052 57.419C19.0576 54.3825 20.3931 51.3396 21.7453 48.303C22.3909 46.8529 23.5788 45.9991 25.3014 45.929C28.1445 45.8131 30.2687 48.261 29.1576 50.8921C27.7091 54.3216 26.1434 57.7101 24.6285 61.1165C24.3521 61.7382 24.0772 62.3605 23.7801 63.0314C24.2092 63.3795 24.5757 63.7598 25.0242 64.0258C26.6954 65.017 28.4817 65.7478 30.506 65.9144C34.0947 66.2098 37.2482 65.4815 39.5056 62.7212C41.5034 60.2783 41.8772 57.6559 40.4039 54.8734C39.5932 53.3425 38.4358 52.0404 37.1359 50.8381C34.6009 48.4935 31.6556 46.6094 28.7373 44.6965C25.5967 42.6377 22.4437 40.5943 19.316 38.5195C18.4452 37.9419 17.6447 37.2771 16.812 36.6522C16.2078 36.0812 15.5446 35.5518 15.0138 34.9293C14.2637 34.0495 13.6115 33.1009 12.9174 32.1817C12.9045 32.1137 12.9069 32.0394 12.8765 31.9786C11.8476 29.9147 11.4662 27.7315 11.4016 25.4855C11.3013 21.9913 12.0287 18.6743 14.0017 15.6403C16.244 12.1918 19.4768 9.77352 23.4851 8.1489C26.5217 6.91818 29.7003 6.28437 33.0012 6.13835C37.615 5.93425 42.0943 6.61149 46.4615 7.94561C47.4594 8.25054 48.4478 8.58132 49.4626 8.90727C50.3527 6.90236 51.28 4.95216 52.0829 2.9605C52.6895 1.45553 53.6803 0.399987 55.4247 0Z"
                  fill="#2458A7"
                />
                <path
                  class="logo-icon"
                  d="M61.4293 17.3108H66.2065C68.3308 17.3197 70.4552 17.3357 72.5795 17.3357C74.7416 17.3356 76.9038 17.3196 79.0659 17.3105C80.1093 17.3105 81.1528 17.3105 82.2397 17.3105C82.4814 16.6209 82.7241 15.9357 82.9617 15.2491C83.6356 13.3025 85.8093 12.2428 87.9217 12.8328C90.0475 13.4266 91.2142 15.4589 90.548 17.4087C89.7385 19.778 88.9285 22.1472 88.1187 24.5164C87.5134 26.2267 86.908 27.937 86.3031 29.6473C86.2169 29.8911 86.1333 30.1356 86.0485 30.3797C86.0028 30.4334 85.9527 30.4845 85.9121 30.5412C84.7456 32.1723 82.6243 32.7534 80.7813 31.9456C78.9182 31.1292 78.0841 29.2351 78.762 27.384C79.0378 26.6307 79.2925 25.8709 79.5526 25.1128C79.6461 24.8404 79.7269 24.5645 79.8383 24.2116H66.8508C65.473 27.6652 64.0931 31.1242 62.6844 34.6553C62.9697 34.6116 63.1348 34.5891 63.2987 34.5608C67.7437 33.7909 72.1577 33.8927 76.5196 35.0112C82.5933 36.5687 87.6532 39.4903 91.2743 44.269C92.121 45.3864 92.8161 46.5986 93.581 47.7673C95.0354 50.8007 96.0776 53.9422 95.9567 57.2763C95.6538 65.6337 90.9819 72.7107 83.0579 77.1835C82.8326 77.3107 83.2826 77.0557 83.0579 77.1835C83.0075 77.2147 83.1082 77.1523 83.0579 77.1835C83.0179 77.2229 83.0979 77.1443 83.0579 77.1835C82.7679 77.285 83.3335 77.0565 83.0579 77.1835C79.4099 78.8651 74.6148 80.1694 70.5251 80.0128C62.0075 79.6865 55.0993 76.5363 50.0989 70.157C50.0774 70.1297 50.0675 70.0948 50.0293 70.0163C51.519 68.5236 53.0239 67.0157 54.5414 65.4952C55.1319 65.5267 55.7173 65.5579 56.1404 65.5805C57.2816 66.7397 58.2374 67.901 59.3971 68.8584C69.2744 77.0136 83.4219 72.2073 87.0103 62.1986C90.2255 53.2312 83.6993 43.3419 73.3653 41.5232C69.705 40.879 66.0924 41.1678 62.5053 41.92C60.9152 42.2534 59.353 42.6967 57.7781 43.0898C57.5969 43.1351 57.4151 43.1785 57.2096 43.2286C55.6099 41.9478 54.0152 40.6708 52.4036 39.3805C53.1658 37.4712 53.9153 35.5908 54.667 33.7109C56.4433 29.2687 58.2394 24.833 59.9756 20.378"
                  fill="#DE8545"
                />
              </g>

              <!-- Gradient definition for shine effect -->
              <defs>
                <linearGradient
                  id="shineGradient"
                  x1="0%"
                  y1="0%"
                  x2="100%"
                  y2="0%"
                >
                  <stop
                    offset="0%"
                    style="stop-color: white; stop-opacity: 0"
                  />
                  <stop
                    offset="50%"
                    style="stop-color: white; stop-opacity: 0.2"
                  />
                  <stop
                    offset="100%"
                    style="stop-color: white; stop-opacity: 0"
                  />
                </linearGradient>
              </defs>

              <!-- Shine effect overlay (hidden initially) -->
              <rect
                id="logo-shine"
                x="-20"
                y="0"
                width="20"
                height="98"
                fill="url(#shineGradient)"
                opacity="0"
              />
            </svg>
            <span class="text-white font-bold text-lg">Serv5</span>
          </div>
          <button
            id="closeMobileMenu"
            class="text-white hover:text-primary-yellow transition-colors duration-300 p-2"
            aria-label="Close mobile menu"
          >
            <i class="fa-solid fa-times text-xl"></i>
          </button>
        </div>

        <!-- Mobile Menu Navigation -->
        <nav class="p-6">
          <ul class="space-y-4 mobile-nav-items">
            <!-- Home -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-solid fa-house text-lg"></i>
                <span data-nav="home" class="font-medium">الرئيسية</span>
              </a>
            </li>
            <!-- About -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-solid fa-5 text-lg"></i>
                <span data-nav="about" class="font-medium">نبذة عنا</span>
              </a>
            </li>
            <!-- Services -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-solid fa-plus text-lg"></i>
                <span data-nav="services" class="font-medium">خدماتنا</span>
              </a>
            </li>
            <!-- Portfolio -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-solid fa-briefcase text-lg"></i>
                <span data-nav="portfolio" class="font-medium">أعمالــنا</span>
              </a>
            </li>
            <!-- Blog -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-regular fa-rectangle-list text-lg"></i>
                <span data-nav="blog" class="font-medium">المدونــة</span>
              </a>
            </li>
            <!-- Brochure -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-solid fa-file-pdf text-lg"></i>
                <span data-nav="brochure" class="font-medium"
                  >الكتيب التعريفي</span
                >
              </a>
            </li>
          </ul>

          <!-- Mobile Menu Actions -->
          <div class="mt-8 space-y-4 mobile-menu-actions">
            <button
              class="w-full rounded-md flex items-center justify-center gap-2 text-white border border-white font-bold bg-transparent p-4 hover:bg-white hover:text-primary-blue transition-all duration-300"
            >
              <i class="fa-solid fa-book"></i>
              <span data-nav="profile">بروفايل الشركة</span>
            </button>

            <!-- Mobile Vision Image -->
            <div class="flex justify-center mt-6">
              <img
                src="/public/shared/header/our-vision.webp"
                alt="Serv5 future vision for 2030"
                class="w-[100px] h-[67px]"
              />
            </div>
          </div>
        </nav>
      </div>
    </header>

    <!-- start of breadcrumbs -->
    <section
      class="bottom-banner bg-light-blue lg:h-[251px] rounded-md container py-10 relative overflow-hidden"
    >
      <!-- flying icons start -->
      <div
        class="absolute flex top-1/2 -translate-y-1/2 left-1/2 lg:start-24 gap-10 items-center justify-evenly w-[580px] h-[218px] z-0"
      >
        <!-- the chat icon -->
        <img
          src="/public/pages/home-page/serv5-features/chat.webp"
          alt="Chat"
          class="floating-icon w-[65px] h-[40px] object-contain float-4 absolute top-0 start-0"
        />
        <!-- the book icon -->
        <img
          src="/public/pages/home-page/serv5-features/book.webp"
          alt="Book"
          class="floating-icon w-[75px] h-[70px] object-contain float-1 absolute top-0 end-10"
        />
        <!-- the envloap icon -->
        <img
          src="/public/pages/home-page/serv5-features/envloap.webp"
          alt="Envelope"
          class="floating-icon w-[40px] h-[36px] object-contain float-3 absolute bottom-10 end-20 lg:end-0"
        />
        <!-- the red envloap icon -->
        <img
          src="/public/pages/home-page/serv5-features/red-envloap.webp"
          alt="Envelope"
          class="floating-icon w-[60px] h-[55px] object-contain float-2 absolute bottom-5 lg:bottom-0 end-10"
        />
        <!-- gray dots -->
        <div
          class="floating-icon float-2 size-3 rounded-full bg-gray absolute top-1/3 end-5"
        ></div>
        <div
          class="floating-icon float-3 size-2 rounded-full bg-gray absolute top-1/3 start-1/4"
        ></div>
        <div
          class="floating-icon float-1 size-4 rounded-full bg-gray absolute top-1/2 start-1/4"
        ></div>
        <!-- orange dots -->
        <div
          class="floating-icon float-4 size-3 rounded-full bg-[#ed835c] absolute top-1/4 start-1/2"
        ></div>
        <div
          class="floating-icon float-1 size-5 rounded-full bg-[#ed835c] absolute bottom-5 start-1/3"
        ></div>
        <div
          class="floating-icon float-3 size-1.5 rounded-full bg-[#ed835c] absolute top-5 start-1/2"
        ></div>

        <!-- the dotted sine wave -->
        <div
          class="absolute hidden lg:inline top-1/2 -translate-y-1/2 inset-x-0 w-full overflow-hidden"
        >
          <img
            src="/public/pages/home-page/serv5-features/dotted-line.svg"
            alt="Dotted line"
          />
        </div>
      </div>
      <!-- flying icons end -->
      <div
        class="absolute top-1/2 -translate-y-1/2 hidden lg:flex end-24 gap-10 items-center justify-evenly w-[580px] h-[218px] z-0"
      >
        <!-- the chat icon -->
        <img
          src="/public/pages/home-page/serv5-features/chat.webp"
          alt="Chat"
          class="floating-icon w-[65px] h-[40px] object-contain float-4 absolute top-0 start-0"
        />
        <!-- the book icon -->
        <img
          src="/public/pages/home-page/serv5-features/book.webp"
          alt="Book"
          class="floating-icon w-[75px] h-[70px] object-contain float-1 absolute top-0 end-10"
        />
        <!-- the envloap icon -->
        <img
          src="/public/pages/home-page/serv5-features/envloap.webp"
          alt="Envelope"
          class="floating-icon w-[40px] h-[36px] object-contain float-3 absolute bottom-10 end-0"
        />
        <!-- the red envloap icon -->
        <img
          src="/public/pages/home-page/serv5-features/red-envloap.webp"
          alt="Envelope"
          class="floating-icon w-[60px] h-[55px] object-contain float-2 absolute bottom-0 end-10"
        />
        <!-- gray dots -->
        <div
          class="floating-icon float-2 size-3 rounded-full bg-gray absolute top-1/3 end-5"
        ></div>

        <div
          class="floating-icon float-3 size-2 rounded-full bg-gray absolute top-1/3 start-1/4"
        ></div>
        <div
          class="floating-icon float-1 size-4 rounded-full bg-gray absolute top-1/2 start-1/4"
        ></div>
        <!-- orange dots -->
        <div
          class="floating-icon float-4 size-3 rounded-full bg-[#ed835c] absolute top-1/4 start-1/2"
        ></div>
        <div
          class="floating-icon float-1 size-5 rounded-full bg-[#ed835c] absolute bottom-5 start-1/3"
        ></div>
        <div
          class="floating-icon float-3 size-1.5 rounded-full bg-[#ed835c] absolute top-5 start-1/2"
        ></div>

        <!-- the dotted sine wave -->
        <div
          class="absolute top-1/2 -translate-y-1/2 inset-x-0 w-full overflow-hidden"
        >
          <img
            src="/public/pages/home-page/serv5-features/dotted-line.svg"
            alt="Dotted line"
          />
        </div>
      </div>

      <div class="flex flex-col">
        <!-- start of navigations -->
        <ul class="flex gap-2 text-white self-start text-sm items-center">
          <li>
            <a href="#" class="hover:text-primary-yellow cursor-pointer"
              >الرئيسية</a
            >
          </li>
          <li>
            <img
              src="/public/shared/cross-pages/serv5-small-logo.webp"
              alt="Serv5 logo"
              class="size-[12px]"
            />
          </li>
          <li>
            <a href="#" class="hover:text-primary-yellow cursor-pointer"
              >سياسة الخصوصية</a
            >
          </li>
        </ul>
        <!-- end of navigations -->
        <p class="banner-text text-white font-bold mt-10 text-2xl lg:text-4xl">
          سياسة الخصوصية
        </p>
      </div>
    </section>
    <!-- end of breadcrumbs -->

    <!-- body of privacy policy -->
    <section class="container grid grid-cols-5">
      <!-- logo -->
      <div class="privacy-logo-container flex justify-center items-center">
        <svg
          id="privacyLogo"
          width="309"
          height="306"
          viewBox="0 0 309 306"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="privacy-logo cursor-pointer"
        >
          <g filter="url(#filter0_d_586_4566)">
            <path
              id="logo-circle-1"
              d="M250.488 233.607C228.55 253.54 198.25 265.876 164.806 265.876C97.9177 265.876 43.6094 216.532 43.6094 155.758C43.6094 94.9836 69.6134 44.5 136.502 44.5"
              stroke="#16215C"
              stroke-width="4.6875"
              stroke-linecap="round"
              fill="none"
            />
            <path
              id="logo-circle-2"
              d="M164.804 45.6406C231.693 45.6406 286.001 94.9845 286.001 155.759C286.001 216.533 231.693 265.877 164.804 265.877C151.734 265.877 139.145 263.993 127.348 260.508"
              stroke="#16215C"
              stroke-width="4.6875"
              stroke-linecap="round"
              fill="none"
            />
            <path
              id="logo-base"
              d="M161.783 282.001H50.2784C33.6255 282.001 20 269.622 20 254.491C20 239.36 33.6255 226.98 50.2784 226.98H161.783C178.436 226.98 192.061 239.36 192.061 254.491C192.061 269.622 178.436 282.001 161.783 282.001Z"
              fill="#15205C"
            />
            <path
              id="logo-text-s"
              d="M59.1065 247.601C58.3083 244.979 56.8959 242.022 53.0279 242.022C49.5895 242.022 47.9626 244.226 47.9626 246.457C47.9626 249.33 50.0807 250.725 53.8259 252.371C57.725 254.1 61.7772 256.024 61.7772 260.515C61.7772 264.895 57.6633 268.381 51.4007 268.381C49.4972 268.381 47.9626 268.074 46.7961 267.712C45.6294 267.377 44.9847 267.042 44.5244 266.847C44.2173 266.038 43.5421 262.328 43.2656 260.348L44.6469 259.985C45.4144 262.579 47.6556 266.847 52.3528 266.847C55.6988 266.847 57.6636 264.978 57.6636 262.049C57.6636 259.093 55.2079 257.698 51.7078 256.024C48.5152 254.574 44.1252 252.538 44.1252 248.047C44.1252 243.975 47.7475 240.6 53.7338 240.6C55.9136 240.6 57.9705 241.074 59.6896 241.576C59.8738 243.026 60.0886 244.7 60.4876 247.378L59.1065 247.601Z"
              fill="#DE8545"
            />
            <path
              id="logo-text-e"
              d="M88.3644 261.127C88.0574 262.661 87.1668 266.427 86.7678 267.738H64.2344V266.567C68.3789 266.288 68.7779 265.897 68.7779 262.55V246.567C68.7779 242.829 68.3789 242.718 64.8176 242.439V241.212H85.632C85.6932 242.272 85.8774 244.977 86.0618 247.264L84.6499 247.432C84.22 245.73 83.7285 244.782 83.1455 243.889C82.4699 242.997 81.2111 242.718 78.0493 242.718H74.6413C73.1371 242.718 73.0452 242.829 73.0452 244.056V253.205H77.251C81.3647 253.205 81.6716 252.871 82.2549 250.109H83.6364V258.115H82.2549C81.6104 255.13 81.2726 254.907 77.251 254.907H73.0452V262.41C73.0452 264.335 73.2909 265.255 74.1502 265.758C75.0406 266.232 76.7597 266.232 78.8473 266.232C82.1934 266.232 83.5749 265.953 84.6189 264.893C85.4169 264.028 86.2765 262.605 86.952 260.96L88.3644 261.127Z"
              fill="#DE8545"
            />
            <path
              id="logo-text-r"
              d="M118.426 268.101C117.874 268.101 117.352 268.073 116.799 268.017C113.085 267.85 111.181 266.957 109.063 264.279C107.497 262.299 105.993 259.816 104.612 257.864C103.783 256.692 102.985 256.19 100.621 256.19H99.3314V262.55C99.3314 265.981 99.7611 266.288 103.445 266.567V267.738H91.0117V266.567C94.7264 266.288 95.1563 265.981 95.1563 262.55V246.372C95.1563 242.885 94.757 242.662 91.1959 242.439V241.212H102.954C106.423 241.212 108.664 241.574 110.414 242.634C112.286 243.694 113.453 245.507 113.453 247.99C113.453 251.532 110.936 253.652 107.651 254.907C108.449 256.246 110.322 258.924 111.703 260.793C113.361 262.94 114.343 264.084 115.418 265.088C116.584 266.288 117.597 266.734 118.641 266.957L118.426 268.101ZM101.388 254.823C103.691 254.823 105.225 254.489 106.392 253.652C108.142 252.425 108.848 250.835 108.848 248.631C108.848 244.252 105.686 242.634 102.278 242.634C100.897 242.634 100.16 242.802 99.8226 243.025C99.5154 243.248 99.3314 243.722 99.3314 244.782V254.823H101.388Z"
              fill="#DE8545"
            />
            <path
              id="logo-text-v"
              d="M148.127 242.439C144.996 242.746 144.351 243.192 142.54 246.93C141.281 249.579 136.492 259.733 132.931 268.184H131.365C128.264 260.709 124.366 251.923 121.787 246.121C120.405 243.081 119.638 242.662 116.875 242.467V241.212H128.572V242.439C125.225 242.746 125.195 243.108 125.901 244.949C127.159 247.878 130.598 255.827 133.514 262.382H133.575C136.093 256.86 139.316 249.412 140.728 245.647C141.649 243.248 141.404 242.829 137.658 242.439V241.212H148.127V242.439Z"
              fill="#DE8545"
            />
            <path
              id="logo-text-5"
              d="M154.607 252.982C156.479 252.34 158.72 251.699 159.917 251.699C165.75 251.699 168.79 255.186 168.79 258.56C168.79 261.043 167.47 263.302 164.615 265.394C162.128 267.124 159.027 268.212 156.571 268.212C154.515 268.212 152.58 267.403 151.844 266.789C151.168 266.204 150.923 265.869 151.015 265.338C151.015 264.892 151.475 264.251 152.059 263.832C152.488 263.554 152.857 263.554 153.317 263.888C154.33 264.697 156.264 266.12 159.119 266.12C162.834 266.12 164.615 263.219 164.615 260.262C164.615 256.636 162.128 254.126 157.247 254.126C155.282 254.126 153.532 254.628 152.458 254.934L154.3 242.438H167.654L167.93 242.745L166.671 245.674H155.804L154.607 252.982Z"
              fill="#DE8545"
            />
            <g filter="url(#filter1_d_586_4566)">
              <path
                id="logo-main-element"
                d="M170.438 16H174.161C174.295 16.067 174.421 16.162 174.564 16.1974C181.671 17.9444 184.931 23.781 182.226 29.9691C180.724 33.4042 179.16 36.8169 177.657 40.2514C177.071 41.5915 176.575 42.9642 176.038 44.322C175.818 44.5426 175.509 44.7303 175.394 44.9889C174.655 46.6583 173.958 48.3427 173.249 50.0231C172.113 52.7146 170.979 55.407 169.844 58.0989C169.622 58.5187 169.373 58.9288 169.181 59.3597C166.375 65.6635 163.576 71.9702 160.775 78.2761C157.974 84.5825 155.188 90.8946 152.367 97.1934C150.306 101.794 145.324 104.41 140.388 103.536C133.377 102.293 129.6 96.0904 132.19 90.0111C133.64 86.6076 135.162 83.2297 136.624 79.9051C136.392 78.9446 136.211 78.0678 135.968 77.2054C134.56 72.2127 132.59 67.456 128.9 63.4285C127.385 61.9866 125.982 60.4275 124.331 59.1277C121.061 56.5519 117.321 54.7435 112.981 54.1138C105.502 52.751 98.5842 53.7532 92.5703 58.2575C88.8248 61.0629 86.4452 64.5698 86.3746 69.1328C86.2628 69.5216 86.023 69.9213 86.062 70.2972C86.2133 71.7631 86.3262 73.2479 86.6854 74.6774C87.5272 78.0283 89.084 81.0862 91.632 83.6781C91.9914 84.2237 92.278 84.8252 92.7223 85.306C95.4521 88.2605 98.6052 90.76 102.204 92.84C106.588 95.3737 110.857 98.07 115.185 100.685C118.089 102.44 121.013 104.167 123.919 105.922C132.349 111.011 140.906 115.975 148.288 122.313C152.752 126.146 157.029 130.158 160.339 134.991C162.807 138.592 164.871 142.32 166.361 146.287C168.939 153.148 169.692 160.224 168.898 167.455C167.788 177.559 163.931 186.652 156.463 194.23C140.338 210.594 119.877 215.867 96.4672 212.805C91.6016 212.168 86.886 210.602 82.0878 209.513C80.0049 209.041 77.8877 208.692 76.0055 208.329C74.4415 209.527 73.6699 210.878 73.3098 212.781C70.071 218.66 66.2552 220.594 59.9558 219.55C59.4977 219.352 59.0447 219.142 58.5804 218.955C54.7809 217.429 52.6387 214.734 52.0914 211.053C51.9032 209.787 52.1741 208.464 52.2351 207.167C54.9966 200.972 57.7579 194.778 60.5194 188.583C63.0332 183.007 65.5681 177.438 68.0561 171.851C71.727 163.61 75.3518 155.35 79.022 147.108C80.7746 143.172 83.9988 140.855 88.6745 140.664C96.3915 140.35 102.157 146.994 99.1412 154.136C95.2097 163.444 90.9597 172.642 86.848 181.888C86.0976 183.575 85.3516 185.264 84.545 187.085C85.7097 188.03 86.7047 189.062 87.9221 189.784C92.458 192.475 97.3066 194.458 102.801 194.91C112.542 195.712 121.101 193.735 127.229 186.243C132.651 179.612 133.666 172.495 129.667 164.942C127.466 160.787 124.325 157.252 120.797 153.989C113.916 147.625 105.922 142.511 98.0005 137.319C89.476 131.731 80.9179 126.184 72.4282 120.553C70.0646 118.985 67.892 117.181 65.6317 115.485C63.9919 113.935 62.1916 112.498 60.7508 110.808C58.715 108.42 56.9447 105.845 55.0607 103.35C55.0257 103.166 55.0321 102.964 54.9498 102.799C52.157 97.197 51.1216 91.2711 50.9464 85.1748C50.6742 75.6906 52.6486 66.6875 58.0038 58.4521C64.0901 49.0921 72.8648 42.5281 83.7445 38.1184C91.9866 34.7779 100.614 33.0576 109.574 32.6612C122.097 32.1073 134.255 33.9455 146.109 37.5666C148.818 38.3943 151.5 39.2922 154.255 40.1769C156.671 34.735 159.188 29.4416 161.367 24.0356C163.014 19.9507 165.703 17.0857 170.438 16Z"
                fill="#2458A7"
              />
            </g>
            <path
              id="logo-accent-element"
              d="M186.736 62.9862H199.703C205.469 63.0104 211.235 63.054 217.001 63.0538C222.869 63.0536 228.738 63.0102 234.607 62.9856C237.439 62.9856 240.271 62.9856 243.221 62.9856C243.877 61.1138 244.536 59.2539 245.181 57.3902C247.01 52.1068 252.91 49.2304 258.644 50.8319C264.414 52.4435 267.581 57.9597 265.772 63.2519C263.575 69.6829 261.376 76.1137 259.179 82.5445C257.536 87.1867 255.892 91.8288 254.25 96.4712C254.016 97.1329 253.789 97.7964 253.559 98.4591C253.435 98.6048 253.299 98.7436 253.189 98.8974C250.023 103.325 244.265 104.902 239.263 102.709C234.206 100.493 231.942 95.3522 233.782 90.3279C234.53 88.2831 235.222 86.2209 235.928 84.1632C236.182 83.4238 236.401 82.6749 236.703 81.717H201.451C197.712 91.0912 193.966 100.48 190.142 110.064C190.917 109.946 191.365 109.885 191.81 109.808C203.875 107.718 215.856 107.994 227.695 111.03C244.181 115.258 257.915 123.188 267.744 136.158C270.042 139.191 271.929 142.482 274.005 145.654C277.952 153.887 280.781 162.414 280.453 171.464C279.631 194.148 266.95 213.358 245.442 225.498C244.83 225.843 246.052 225.151 245.442 225.498C245.305 225.583 245.579 225.413 245.442 225.498C245.333 225.605 245.551 225.391 245.442 225.498C244.655 225.773 246.19 225.153 245.442 225.498C235.54 230.062 222.525 233.603 211.425 233.177C188.305 232.292 169.554 223.741 155.982 206.426C155.924 206.352 155.897 206.257 155.793 206.044C159.836 201.992 163.921 197.9 168.04 193.772C169.643 193.858 171.232 193.943 172.38 194.004C175.478 197.15 178.072 200.302 181.22 202.901C208.03 225.037 246.43 211.991 256.17 184.825C264.897 160.484 247.183 133.642 219.134 128.706C209.198 126.957 199.393 127.741 189.656 129.783C185.341 130.688 181.1 131.891 176.825 132.958C176.334 133.081 175.84 133.199 175.282 133.335C170.94 129.858 166.612 126.392 162.238 122.89C164.306 117.708 166.341 112.603 168.381 107.501C173.202 95.4436 178.077 83.4038 182.79 71.3116"
              fill="#DE8545"
            />
          </g>
          <defs>
            <filter
              id="filter0_d_586_4566"
              x="0"
              y="0"
              width="308.344"
              height="306.001"
              filterUnits="userSpaceOnUse"
              color-interpolation-filters="sRGB"
            >
              <feFlood flood-opacity="0" result="BackgroundImageFix" />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feOffset dy="4" />
              <feGaussianBlur stdDeviation="10" />
              <feComposite in2="hardAlpha" operator="out" />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 0.141176 0 0 0 0 0.345098 0 0 0 0 0.654902 0 0 0 0.35 0"
              />
              <feBlend
                mode="normal"
                in2="BackgroundImageFix"
                result="effect1_dropShadow_586_4566"
              />
              <feBlend
                mode="normal"
                in="SourceGraphic"
                in2="effect1_dropShadow_586_4566"
                result="shape"
              />
            </filter>
            <filter
              id="filter1_d_586_4566"
              x="46.9219"
              y="16"
              width="140.25"
              height="211.82"
              filterUnits="userSpaceOnUse"
              color-interpolation-filters="sRGB"
            >
              <feFlood flood-opacity="0" result="BackgroundImageFix" />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feOffset dy="4" />
              <feGaussianBlur stdDeviation="2" />
              <feComposite in2="hardAlpha" operator="out" />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
              />
              <feBlend
                mode="normal"
                in2="BackgroundImageFix"
                result="effect1_dropShadow_586_4566"
              />
              <feBlend
                mode="normal"
                in="SourceGraphic"
                in2="effect1_dropShadow_586_4566"
                result="shape"
              />
            </filter>
          </defs>
        </svg>
      </div>
      <!-- content -->
      <div class=""></div>
    </section>

    <footer class="bg-primary-blue text-white">
      <!-- first part -->
      <div
        class="grid grid-cols-2 sm:grid-cols-3 gap-x-2 gap-y-5 lg:grid-cols-4 xl:grid-cols-7 lg:gap-4 xl:gap-3 container pt-[93px] pb-12"
      >
        <!-- first col -->
        <div class="space-y-5 col-span-2 xl:col-span-2">
          <div class="flex items-center gap-6">
            <img
              src="/public/pages/home-page/footer/serv5-logo.svg"
              alt="Serv5 Logo"
              class="size-[98px]"
            />
            <p
              class="font-bold text-xl border-b border-b-primary-yellow pb-2 max-w-[300px]"
            >
              عن سيرف
            </p>
          </div>

          <p class="text-sm leading-7 max-2-[410px]">
            لقد وصلت لوجهتك الصحيحة؛ ما تبحث عنه امامك معنا سوف تحصل على خدمة
            تصميم مواقع ، برمجة مواقع ، برمجة تطبيقات جوال ، خدمات تسويقية
            متميزة.
          </p>
          <!-- the images of our projects  -->
          <div class="flex gap-2 items-center">
            <div
              class="w-[70px] h-[32px] bg-white rounded-md flex itecems-center justify-center hover:scale-105 cursor-pointer"
            >
              <img
                src="/public/pages/home-page/footer/blmgan.webp"
                alt="Blmgan app"
                class="object-contain w-[52px]"
              />
            </div>
            <div
              class="w-[70px] h-[32px] bg-white rounded-md flex itecems-center justify-center hover:scale-105 cursor-pointer"
            >
              <img
                src="/public/pages/home-page/footer/makok.webp"
                alt="Makok Website"
                class="object-contain w-[52px]"
              />
            </div>
            <div
              class="w-[70px] h-[32px] bg-white rounded-md flex itecems-center justify-center hover:scale-105 cursor-pointer"
            >
              <img
                src="/public/pages/home-page/footer/needbots.webp"
                alt="Chat app neat bot"
                class="object-contain w-[52px]"
              />
            </div>
          </div>
        </div>
        <!-- second col -->
        <div class="flex flex-col gap-4">
          <p
            class="font-bold text-xl border-b w-fit border-b-primary-yellow pb-2"
          >
            روابط هامة
          </p>

          <ul class="mt-4 lg:mt-10 flex flex-col gap-3">
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
          </ul>
        </div>
        <!-- third col -->
        <div class="flex flex-col gap-4">
          <p
            class="font-bold text-xl border-b w-fit border-b-primary-yellow pb-2"
          >
            الخدمات
          </p>

          <ul class="mt-4 lg:mt-10 flex flex-col gap-3">
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
          </ul>
        </div>
        <!-- fourth col -->
        <div class="flex flex-col gap-4">
          <p
            class="font-bold text-xl border-b w-fit border-b-primary-yellow pb-2"
          >
            تواصل معنا
          </p>

          <ul class="mt-4 lg:mt-10 flex flex-col gap-3">
            <li>
              <a
                href="#"
                class="sm hover:text-primary-yellow cursor-pointer group"
              >
                <i
                  class="fa-solid fa-envelope text-primary-yellow me-2 group-hover:scale-110"
                ></i>
                عن سيرف</a
              >
            </li>
            <li>
              <a
                href="#"
                class="sm hover:text-primary-yellow cursor-pointer group"
              >
                <i
                  class="fa-solid fa-phone text-primary-yellow me-2 group-hover:scale-110"
                ></i>
                عن سيرف</a
              >
            </li>
            <li>
              <a
                href="#"
                class="sm hover:text-primary-yellow cursor-pointer group"
              >
                <i
                  class="fa-solid fa-location-pin-lock text-primary-yellow me-2 group-hover:scale-110"
                ></i>
                عن سيرف</a
              >
            </li>
          </ul>
        </div>
        <!-- fifth col -->
        <div class="flex flex-col gap-4 col-span-1 xl:col-span-2">
          <p
            class="font-bold text-xl border-b w-fit border-b-primary-yellow pb-2"
          >
            إشترك معنا
          </p>

          <p class="mt-4 lg:mt-10 flex flex-col gap-3">
            قم بكتابة بريدك الإلكتروني وإشترك في النشرة البريدية
          </p>

          <!-- Newsletter Subscription Form -->
          <form id="newsletter-form" class="md:mt-6 max-w-[350px]">
            <div class="newsletter-input-group relative">
              <i
                class="fa-solid fa-arrow-right absolute text-primary-yellow top-1/2 -translate-y-1/2"
              ></i>
              <input
                type="email"
                id="newsletter-email"
                name="email"
                placeholder="أدخل بريدك الإلكتروني"
                class="w-full bg-transparent text-white placeholder-gray-300 py-3 border-0 border-b border-gray-400 ps-5 placeholder:text-sm focus:border-primary-yellow focus:outline-none transition-colors duration-300"
                required
              />
              <div
                id="email-error"
                class="text-red-400 text-xs mt-1 hidden"
              ></div>
            </div>

            <div
              id="newsletter-success"
              class="text-green-400 text-sm mt-2 hidden"
            >
              تم الاشتراك بنجاح! شكراً لك.
            </div>
          </form>
        </div>
      </div>
      <!-- second part -->
      <div
        class="container flex flex-col gap-5 sm:flex-row justify-between items-center py-5 w-full border-t border-t-gray"
      >
        <!-- go up and terms and condtions links -->
        <div class="flex gap-4 items-center">
          <button
            id="goToTopBtn"
            class="size-12 p-5 text-white bg-primary-yellow hover:scale-110 transition-colors cursor-pointer flex items-center justify-center"
          >
            <i class="fa-solid fa-arrow-up text-xl"></i>
          </button>

          <a href="#" class="hover:text-primary-yellow">سياسة الخصوصية </a>
          <div class="w-[1px] h-6 bg-white my-auto"></div>
          <a href="#" class="hover:text-primary-yellow">الشروط والأحكــام</a>
        </div>

        <!-- all right reserved text -->
        <p class="text-sm text-center">
          © 2025 جميع الحقوق محفوظة لشركة سيرف. جميع الحقوق محفوظة.
        </p>
      </div>
    </footer>

    <!-- Privacy Logo Animation Script -->
    <script>
      // Wait for both DOM and GSAP to be ready
      function initPrivacyLogoAnimation() {
        const privacyLogo = document.getElementById("privacyLogo");
        const logoContainer = document.querySelector(".privacy-logo-container");

        if (!privacyLogo || !logoContainer) {
          console.log("Privacy logo elements not found");
          return;
        }

        if (typeof gsap === "undefined") {
          console.log("GSAP not loaded, retrying in 100ms...");
          setTimeout(initPrivacyLogoAnimation, 100);
          return;
        }

        console.log("Initializing privacy logo animation");

        // Set initial states
        gsap.set(privacyLogo, {
          scale: 0.2,
          autoAlpha: 0,
          rotationX: 180,
        });

        // Set initial states for individual elements
        gsap.set("#logo-circle-1, #logo-circle-2", {
          strokeDasharray: "1000",
          strokeDashoffset: "1000",
        });

        gsap.set("#logo-base, #logo-main-element, #logo-accent-element", {
          autoAlpha: 0,
          scale: 0.8,
        });

        gsap.set(
          "#logo-text-s, #logo-text-e, #logo-text-r, #logo-text-v, #logo-text-5",
          {
            autoAlpha: 0,
            y: 20,
          }
        );

        // Main entrance animation timeline
        const logoTimeline = gsap.timeline({
          scrollTrigger: {
            trigger: logoContainer,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        });

        // Logo entrance with scale and rotation
        logoTimeline
          .to(privacyLogo, {
            scale: 1,
            autoAlpha: 1,
            rotationX: 0,
            duration: 1.5,
            ease: "back.out(1.7)",
          })
          // Animate circles with stroke dash
          .to(
            "#logo-circle-1",
            {
              strokeDashoffset: 0,
              duration: 1.2,
              ease: "power2.out",
            },
            "-=1"
          )
          .to(
            "#logo-circle-2",
            {
              strokeDashoffset: 0,
              duration: 1.2,
              ease: "power2.out",
            },
            "-=0.8"
          )
          // Animate base and main elements
          .to(
            "#logo-base",
            {
              autoAlpha: 1,
              scale: 1,
              duration: 0.8,
              ease: "power2.out",
            },
            "-=0.6"
          )
          .to(
            "#logo-main-element",
            {
              autoAlpha: 1,
              scale: 1,
              duration: 0.8,
              ease: "power2.out",
            },
            "-=0.4"
          )
          .to(
            "#logo-accent-element",
            {
              autoAlpha: 1,
              scale: 1,
              duration: 0.8,
              ease: "power2.out",
            },
            "-=0.2"
          )
          // Animate text letters sequentially
          .to(
            "#logo-text-s, #logo-text-e, #logo-text-r, #logo-text-v, #logo-text-5",
            {
              autoAlpha: 1,
              y: 0,
              duration: 0.6,
              stagger: 0.1,
              ease: "power2.out",
            },
            "-=0.4"
          );

        // Continuous floating animation
        gsap.to(privacyLogo, {
          y: -10,
          duration: 3,
          ease: "power1.inOut",
          yoyo: true,
          repeat: -1,
        });

        gsap.to(privacyLogo, {
          x: 5,
          duration: 4,
          ease: "power1.inOut",
          yoyo: true,
          repeat: -1,
        });

        // Hover interaction
        privacyLogo.addEventListener("mouseenter", function () {
          gsap.to(privacyLogo, {
            scale: 1.1,
            rotationY: 360,
            duration: 1.5,
            ease: "power2.out",
          });
        });

        privacyLogo.addEventListener("mouseleave", function () {
          gsap.to(privacyLogo, {
            scale: 1,
            rotationY: 0,
            duration: 0.8,
            ease: "power2.out",
          });
        });

        // Click interaction - replay entrance animation
        privacyLogo.addEventListener("click", function () {
          // Reset and replay the entrance animation
          gsap.set("#logo-circle-1, #logo-circle-2", {
            strokeDashoffset: "1000",
          });

          gsap.set("#logo-base, #logo-main-element, #logo-accent-element", {
            autoAlpha: 0,
            scale: 0.8,
          });

          gsap.set(
            "#logo-text-s, #logo-text-e, #logo-text-r, #logo-text-v, #logo-text-5",
            {
              autoAlpha: 0,
              y: 20,
            }
          );

          // Replay animation
          const replayTimeline = gsap.timeline();

          replayTimeline
            .to("#logo-circle-1", {
              strokeDashoffset: 0,
              duration: 0.8,
              ease: "power2.out",
            })
            .to(
              "#logo-circle-2",
              {
                strokeDashoffset: 0,
                duration: 0.8,
                ease: "power2.out",
              },
              "-=0.6"
            )
            .to(
              "#logo-base",
              {
                autoAlpha: 1,
                scale: 1,
                duration: 0.6,
                ease: "power2.out",
              },
              "-=0.4"
            )
            .to(
              "#logo-main-element",
              {
                autoAlpha: 1,
                scale: 1,
                duration: 0.6,
                ease: "power2.out",
              },
              "-=0.3"
            )
            .to(
              "#logo-accent-element",
              {
                autoAlpha: 1,
                scale: 1,
                duration: 0.6,
                ease: "power2.out",
              },
              "-=0.2"
            )
            .to(
              "#logo-text-s, #logo-text-e, #logo-text-r, #logo-text-v, #logo-text-5",
              {
                autoAlpha: 1,
                y: 0,
                duration: 0.4,
                stagger: 0.08,
                ease: "power2.out",
              },
              "-=0.3"
            );
        });
      }

      // Initialize when DOM is ready
      document.addEventListener("DOMContentLoaded", function () {
        // Small delay to ensure GSAP is loaded
        setTimeout(initPrivacyLogoAnimation, 100);
      });

      // Also try to initialize immediately if DOM is already loaded
      if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", initPrivacyLogoAnimation);
      } else {
        setTimeout(initPrivacyLogoAnimation, 100);
      }
    </script>

    <script type="module" src="/src/main.js"></script>
  </body>
</html>
